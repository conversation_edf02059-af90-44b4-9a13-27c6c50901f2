from tkinter import filedialog, messagebox
import threading
try:
    import Tkinter as tk
except ImportError:
    import tkinter as tk

try:
    import ttk
    py3 = False
except ImportError:
    import tkinter.ttk as ttk
    py3 = True

import VideoSplitting_support
import subprocess
import os
import re
import queue
import time

def center_window(window, width, height):
    """Center the window on the screen."""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    x = (screen_width // 2) - (width // 2)
    y = (screen_height // 2) - (height // 2)
    window.geometry(f'{width}x{height}+{x}+{y}')

def vp_start_gui():
    global val, w, root
    root = tk.Tk()
    top = Toplevel1 (root)
    VideoSplitting_support.init(root, top)
    center_window(root, 900, 500)
    root.mainloop()

w = None
def create_Toplevel1(root, *args, **kwargs):
    global w, w_win, rt
    rt = root
    w = tk.Toplevel (root)
    top = Toplevel1 (w)
    VideoSplitting_support.init(w, top, *args, **kwargs)
    return (w, top)

def destroy_Toplevel1():
    global w
    w.destroy()
    w = None

class Toplevel1:
    def __init__(self, top=None):
        # 现代化配色方案 - Bootstrap风格
        self.colors = {
            'bg_primary': '#f8f9fa',      # 主背景色
            'bg_secondary': '#ffffff',     # 次要背景色
            'primary': '#007bff',          # 主色调
            'secondary': '#6c757d',        # 次要色调
            'success': '#28a745',          # 成功色
            'danger': '#dc3545',           # 危险色
            'warning': '#ffc107',          # 警告色
            'text_primary': '#343a40',     # 主文本色
            'text_secondary': '#6c757d',   # 次要文本色
            'border': '#dee2e6',           # 边框色
            'hover': '#0056b3'             # 悬停色
        }

        # 字体配置
        self.fonts = {
            'default': ('Segoe UI', 9),
            'heading': ('Segoe UI', 11, 'bold'),
            'button': ('Segoe UI', 9, 'bold'),
            'small': ('Segoe UI', 8)
        }

        top.geometry("900x500")
        top.title("视频分割工具 - 现代化版本")
        top.configure(background='#f0f2f5')
        top.resizable(False, False)

        # 创建主容器 - 使用卡片式设计
        self.main_frame = tk.Frame(top)
        self.main_frame.place(x=30, y=30, width=840, height=440)
        self.main_frame.configure(background='white', relief='flat', bd=0)

        # 添加阴影效果（通过背景框架模拟）
        shadow_frame = tk.Frame(top)
        shadow_frame.place(x=33, y=33, width=840, height=440)
        shadow_frame.configure(background='#e0e0e0')
        shadow_frame.lower()  # 放到最底层

        # 标题区域
        title_label = tk.Label(self.main_frame)
        title_label.place(x=30, y=20, width=200, height=30)
        title_label.configure(text="视频分割工具", font=('Microsoft YaHei', 16, 'bold'))
        title_label.configure(background='white', foreground='#2c3e50')

        # 文件选择区域 - 简洁设计
        file_section = tk.Frame(self.main_frame)
        file_section.place(x=30, y=70, width=780, height=80)
        file_section.configure(background='#f8f9fa', relief='flat')

        self.Label1 = tk.Label(file_section)
        self.Label1.place(x=20, y=15, width=80, height=25)
        self.Label1.configure(background='#f8f9fa', foreground='#495057')
        self.Label1.configure(font=('Microsoft YaHei', 10))
        self.Label1.configure(text='选择文件:')

        self.Text1 = tk.Text(file_section)
        self.Text1.place(x=20, y=45, width=600, height=25)
        self.Text1.configure(background='white', foreground='#495057')
        self.Text1.configure(font=('Microsoft YaHei', 9))
        self.Text1.configure(relief='solid', bd=1, highlightthickness=0)
        self.Text1.configure(wrap='none')

        self.Button1 = tk.Button(file_section)
        self.Button1.place(x=640, y=43, width=120, height=30)
        self.Button1.configure(background='#007bff', foreground='white')
        self.Button1.configure(font=('Microsoft YaHei', 9, 'bold'))
        self.Button1.configure(relief='flat', cursor='hand2')
        self.Button1.configure(text='浏览文件')
        self.Button1.config(command=self.load_file)
        # 悬停效果
        self.Button1.bind("<Enter>", lambda e: self.Button1.configure(background='#0056b3'))
        self.Button1.bind("<Leave>", lambda e: self.Button1.configure(background='#007bff'))

        # 时间设置区域
        time_section = tk.Frame(self.main_frame)
        time_section.place(x=30, y=170, width=780, height=120)
        time_section.configure(background='#f8f9fa', relief='flat')

        # 时间设置标题
        time_title = tk.Label(time_section)
        time_title.place(x=20, y=15, width=200, height=25)
        time_title.configure(text="时间设置", font=('Microsoft YaHei', 12, 'bold'))
        time_title.configure(background='#f8f9fa', foreground='#2c3e50')

        # 提示文字
        time_hint = tk.Label(time_section)
        time_hint.place(x=220, y=18, width=300, height=20)
        time_hint.configure(text="(使用鼠标滚轮调整数值)", font=('Microsoft YaHei', 8))
        time_hint.configure(background='#f8f9fa', foreground='#6c757d')

        # 开始时间
        self.Label2 = tk.Label(time_section)
        self.Label2.place(x=50, y=55, width=80, height=25)
        self.Label2.configure(background='#f8f9fa', foreground='#495057')
        self.Label2.configure(font=('Microsoft YaHei', 10))
        self.Label2.configure(text='开始时间:')

        # 开始时间控件
        start_frame = tk.Frame(time_section)
        start_frame.place(x=140, y=50, width=200, height=35)
        start_frame.configure(background='#f8f9fa')

        self.start_hour = tk.Spinbox(start_frame)
        self.start_hour.place(x=0, y=0, width=50, height=30)
        self.start_hour.configure(from_=0, to=23, font=('Consolas', 11), format='%02.0f')
        self.start_hour.configure(background='white', relief='solid', bd=1)
        self.start_hour.delete(0, tk.END)
        self.start_hour.insert(0, '00')
        self.bind_scroll_wheel(self.start_hour)

        tk.Label(start_frame, text=':', font=('Consolas', 14, 'bold'),
                background='#f8f9fa', foreground='#495057').place(x=55, y=5)

        self.start_minute = tk.Spinbox(start_frame)
        self.start_minute.place(x=75, y=0, width=50, height=30)
        self.start_minute.configure(from_=0, to=59, font=('Consolas', 11), format='%02.0f')
        self.start_minute.configure(background='white', relief='solid', bd=1)
        self.start_minute.delete(0, tk.END)
        self.start_minute.insert(0, '00')
        self.bind_scroll_wheel(self.start_minute)

        tk.Label(start_frame, text=':', font=('Consolas', 14, 'bold'),
                background='#f8f9fa', foreground='#495057').place(x=130, y=5)

        self.start_second = tk.Spinbox(start_frame)
        self.start_second.place(x=150, y=0, width=50, height=30)
        self.start_second.configure(from_=0, to=59, font=('Consolas', 11), format='%02.0f')
        self.start_second.configure(background='white', relief='solid', bd=1)
        self.start_second.delete(0, tk.END)
        self.start_second.insert(0, '00')
        self.bind_scroll_wheel(self.start_second)

        # 结束时间
        self.Label3 = tk.Label(time_section)
        self.Label3.place(x=400, y=55, width=80, height=25)
        self.Label3.configure(background='#f8f9fa', foreground='#495057')
        self.Label3.configure(font=('Microsoft YaHei', 10))
        self.Label3.configure(text='结束时间:')

        # 结束时间控件
        end_frame = tk.Frame(time_section)
        end_frame.place(x=490, y=50, width=200, height=35)
        end_frame.configure(background='#f8f9fa')

        self.end_hour = tk.Spinbox(end_frame)
        self.end_hour.place(x=0, y=0, width=50, height=30)
        self.end_hour.configure(from_=0, to=23, font=('Consolas', 11), format='%02.0f')
        self.end_hour.configure(background='white', relief='solid', bd=1)
        self.end_hour.delete(0, tk.END)
        self.end_hour.insert(0, '00')
        self.bind_scroll_wheel(self.end_hour)

        tk.Label(end_frame, text=':', font=('Consolas', 14, 'bold'),
                background='#f8f9fa', foreground='#495057').place(x=55, y=5)

        self.end_minute = tk.Spinbox(end_frame)
        self.end_minute.place(x=75, y=0, width=50, height=30)
        self.end_minute.configure(from_=0, to=59, font=('Consolas', 11), format='%02.0f')
        self.end_minute.configure(background='white', relief='solid', bd=1)
        self.end_minute.delete(0, tk.END)
        self.end_minute.insert(0, '00')
        self.bind_scroll_wheel(self.end_minute)

        tk.Label(end_frame, text=':', font=('Consolas', 14, 'bold'),
                background='#f8f9fa', foreground='#495057').place(x=130, y=5)

        self.end_second = tk.Spinbox(end_frame)
        self.end_second.place(x=150, y=0, width=50, height=30)
        self.end_second.configure(from_=0, to=59, font=('Consolas', 11), format='%02.0f')
        self.end_second.configure(background='white', relief='solid', bd=1)
        self.end_second.delete(0, tk.END)
        self.end_second.insert(0, '00')
        self.bind_scroll_wheel(self.end_second)

        # 操作区域 - 现代化设计
        action_section = tk.Frame(self.main_frame)
        action_section.place(x=30, y=310, width=780, height=70)
        action_section.configure(background='white', relief='flat')

        # 处理方式选择：固定为GPU模式
        self.process_mode = tk.IntVar()
        self.process_mode.set(0)

        # 说明文字
        self.info_label = tk.Label(action_section)
        self.info_label.place(x=50, y=25, width=400, height=25)
        self.info_label.configure(background='white', foreground='#6c757d')
        self.info_label.configure(font=('Microsoft YaHei', 10))
        self.info_label.configure(text='🚀 使用NVIDIA GPU加速处理，速度快且画面流畅')

        # 开始分割按钮 - 真正的居中
        self.Button2 = tk.Button(action_section)
        self.Button2.place(x=580, y=15, width=150, height=40)  # 在70px高度中居中：(70-40)/2=15
        self.Button2.configure(background='#28a745', foreground='white')
        self.Button2.configure(font=('Microsoft YaHei', 11, 'bold'))
        self.Button2.configure(relief='flat', cursor='hand2')
        self.Button2.configure(text='开始分割')
        self.Button2.config(command=self.split_media)
        # 悬停效果
        self.Button2.bind("<Enter>", lambda e: self.Button2.configure(background='#218838'))
        self.Button2.bind("<Leave>", lambda e: self.Button2.configure(background='#28a745'))

        # 进度显示区域 - 现代化设计，充足的底部空间
        self.progress_section = tk.Frame(self.main_frame)
        self.progress_section.place(x=30, y=390, width=780, height=40)  # 给底部留出10px空间
        self.progress_section.configure(background='#f8f9fa', relief='flat')
        self.progress_section.place_forget()  # 初始隐藏

        # 进度标签
        self.progress_label = tk.Label(self.progress_section)
        self.progress_label.place(x=20, y=5, width=740, height=20)
        self.progress_label.configure(background='#f8f9fa', foreground='#495057')
        self.progress_label.configure(font=('Microsoft YaHei', 9))
        self.progress_label.configure(text="", anchor='w')

        # 进度条
        self.progressbar = ttk.Progressbar(self.progress_section)
        self.progressbar.place(x=20, y=25, width=740, height=8)
        self.progressbar.configure(mode='determinate', maximum=100)

        # 进度队列用于线程间通信
        self.progress_queue = queue.Queue()
        self.total_frames = 0
        self.last_error = ""

        # 进程管理
        self.current_process = None
        self.is_processing = False

        # 绑定窗口关闭事件
        top.protocol("WM_DELETE_WINDOW", self.on_closing)

    def bind_scroll_wheel(self, spinbox):
        '''为Spinbox绑定鼠标滚轮事件'''
        def on_scroll(event):
            if event.delta > 0:  # 向上滚动
                spinbox.invoke('buttonup')
            else:  # 向下滚动
                spinbox.invoke('buttondown')

        spinbox.bind("<MouseWheel>", on_scroll)

    def get_start_time(self):
        '''获取开始时间字符串'''
        hour = self.start_hour.get().zfill(2)
        minute = self.start_minute.get().zfill(2)
        second = self.start_second.get().zfill(2)
        return f"{hour}:{minute}:{second}"

    def get_end_time(self):
        '''获取结束时间字符串'''
        hour = self.end_hour.get().zfill(2)
        minute = self.end_minute.get().zfill(2)
        second = self.end_second.get().zfill(2)
        return f"{hour}:{minute}:{second}"

    def set_start_time(self, time_str):
        '''设置开始时间'''
        try:
            parts = time_str.split(':')
            self.start_hour.delete(0, tk.END)
            self.start_hour.insert(0, f"{int(parts[0]):02d}")
            self.start_minute.delete(0, tk.END)
            self.start_minute.insert(0, f"{int(parts[1]):02d}")
            self.start_second.delete(0, tk.END)
            self.start_second.insert(0, f"{int(parts[2]):02d}")
        except:
            pass

    def set_end_time(self, time_str):
        '''设置结束时间'''
        try:
            parts = time_str.split(':')
            self.end_hour.delete(0, tk.END)
            self.end_hour.insert(0, f"{int(parts[0]):02d}")
            self.end_minute.delete(0, tk.END)
            self.end_minute.insert(0, f"{int(parts[1]):02d}")
            self.end_second.delete(0, tk.END)
            self.end_second.insert(0, f"{int(parts[2]):02d}")
        except:
            pass

    def get_video_duration(self, file_path):
        '''获取视频时长'''
        try:
            command = f'ffprobe -v quiet -show_entries format=duration -of csv="p=0" "{file_path}"'
            result = subprocess.run(command, shell=True, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                duration_seconds = float(result.stdout.strip())
                hours = int(duration_seconds // 3600)
                minutes = int((duration_seconds % 3600) // 60)
                seconds = int(duration_seconds % 60)
                return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        except Exception as e:
            print(f"获取视频时长失败: {e}")
        return None

    def get_video_frame_count(self, file_path, start_time, end_time):
        '''获取视频片段的总帧数'''
        try:
            # 计算时长（秒）
            start_seconds = self.time_to_seconds(start_time)
            end_seconds = self.time_to_seconds(end_time)
            duration = end_seconds - start_seconds

            # 获取帧率
            command = f'ffprobe -v quiet -select_streams v:0 -show_entries stream=r_frame_rate -of csv="p=0" "{file_path}"'
            result = subprocess.run(command, shell=True, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                fps_str = result.stdout.strip()
                if '/' in fps_str:
                    num, den = fps_str.split('/')
                    fps = float(num) / float(den)
                else:
                    fps = float(fps_str)

                # 计算总帧数
                total_frames = int(duration * fps)
                return total_frames
        except Exception as e:
            print(f"获取视频帧数失败: {e}")
        return None

    def time_to_seconds(self, time_str):
        '''将时间字符串转换为秒数'''
        try:
            parts = time_str.split(':')
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds = float(parts[2])
            return hours * 3600 + minutes * 60 + seconds
        except:
            return 0

    def check_gpu_support(self):
        '''检查NVIDIA GPU编码器支持情况'''
        gpu_support = {
            'nvidia': False
        }

        try:
            # 只检查NVIDIA GPU支持（用户只有NVIDIA GPU）
            result = subprocess.run('ffmpeg -hide_banner -encoders 2>&1 | findstr nvenc',
                                  shell=True, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore')
            if result.returncode == 0 and 'h264_nvenc' in result.stdout:
                gpu_support['nvidia'] = True

        except Exception as e:
            print(f"检查NVIDIA GPU支持时出错: {e}")

        return gpu_support

    def load_file(self):
        '''Load file and display its path in Text1'''
        filename = filedialog.askopenfilename()
        if filename:
            self.Text1.delete(1.0, tk.END)
            self.Text1.insert(tk.END, filename)

            # 自动获取视频时长并填写到时间框
            duration = self.get_video_duration(filename)
            if duration:
                # 设置开始时间为00:00:00
                self.set_start_time('00:00:00')

                # 设置结束时间为视频总时长
                self.set_end_time(duration)
            else:
                messagebox.showwarning("警告", "无法获取视频时长，请手动填写时间")

    def start_progress(self):
        '''显示并启动进度条'''
        self.progress_section.place(x=30, y=390, width=780, height=40)
        self.progressbar['value'] = 0

    def stop_progress(self):
        '''停止并隐藏进度条'''
        self.progress_section.place_forget()

    def update_progress(self, current_frame, total_frames, status=""):
        '''更新进度显示'''
        if total_frames > 0:
            progress = (current_frame / total_frames) * 100
            self.progressbar['value'] = progress

            # 更新标签文字
            label_text = f"{status} - {current_frame}/{total_frames}帧 ({progress:.1f}%)"
            self.progress_label.config(text=label_text)

    def check_progress_queue(self):
        '''检查进度队列并更新UI'''
        try:
            while True:
                progress_data = self.progress_queue.get_nowait()
                if progress_data['type'] == 'progress':
                    self.update_progress(progress_data['current'], progress_data['total'], progress_data['status'])
                elif progress_data['type'] == 'status':
                    self.progress_label.config(text=progress_data['message'])
        except queue.Empty:
            pass

        # 继续检查队列
        if hasattr(self, '_progress_check_id'):
            self.progress_label.after(100, self.check_progress_queue)

    def run_ffmpeg_with_progress(self, command, total_frames, status="处理中"):
        '''运行ffmpeg并实时显示进度'''
        try:
            print(f"执行命令: {command}")
            process = subprocess.Popen(command, shell=True, stderr=subprocess.PIPE,
                                     stdout=subprocess.PIPE, universal_newlines=True, bufsize=1,
                                     encoding='utf-8', errors='ignore')

            # 保存当前进程引用
            self.current_process = process
            self.is_processing = True

            frame_pattern = re.compile(r'frame=\s*(\d+)')
            error_output = []

            for line in process.stderr:
                # 检查进程是否被终止
                if not self.is_processing:
                    break

                error_output.append(line)
                print(f"ffmpeg输出: {line.strip()}")  # 调试信息

                # 解析当前帧数
                match = frame_pattern.search(line)
                if match:
                    current_frame = int(match.group(1))
                    # 将进度信息放入队列
                    self.progress_queue.put({
                        'type': 'progress',
                        'current': current_frame,
                        'total': total_frames,
                        'status': status
                    })

            process.wait()

            # 如果失败，保存错误信息
            if process.returncode != 0:
                self.last_error = ''.join(error_output)
                print(f"ffmpeg失败，返回码: {process.returncode}")
                print(f"错误信息: {self.last_error}")

            return process.returncode

        except Exception as e:
            print(f"执行ffmpeg时出错: {e}")
            self.last_error = str(e)
            return 1
        finally:
            # 清理进程引用
            self.current_process = None
            self.is_processing = False

    def split_media(self):
        '''Split media based on start and end times'''
        self.Button2.config(state=tk.DISABLED, text='分割中')  # 设置按钮为不可点击并更改文本
        self.start_progress()  # 显示进度条

        # 启动进度检查
        self._progress_check_id = True
        self.check_progress_queue()

        thread = threading.Thread(target=self._split_media)
        thread.start()

    def _split_media(self):
        '''Internal method to handle the actual media splitting process.'''
        self.is_processing = True  # 设置处理状态

        file_path = self.Text1.get(1.0, tk.END).strip()
        start_time = self.get_start_time()
        end_time = self.get_end_time()
        if file_path and start_time and end_time:
            extension = os.path.splitext(file_path)[1].lower()
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_dir = os.path.dirname(file_path)

            # 根据用户选择的处理方式生成命令
            mode = self.process_mode.get()

            # 获取视频帧数用于进度显示
            self.progress_queue.put({
                'type': 'status',
                'message': '正在分析视频信息...'
            })

            self.total_frames = self.get_video_frame_count(file_path, start_time, end_time)
            if not self.total_frames:
                self.total_frames = 1000  # 默认值，避免除零错误

            # 检查GPU支持情况
            gpu_support = self.check_gpu_support()
            print(f"GPU支持情况: {gpu_support}")

            if mode == 0:  # GPU加速模式
                output_file = os.path.join(output_dir, f"{base_name}_split_gpu{extension}")
                # 只处理NVIDIA GPU（用户只有NVIDIA GPU）
                gpu_command = None

                if gpu_support['nvidia']:
                    gpu_command = f'ffmpeg -y -hwaccel cuda -i "{file_path}" -ss {start_time} -to {end_time} -c:v h264_nvenc -preset fast -c:a aac -avoid_negative_ts make_zero "{output_file}"'

                fallback_command = f'ffmpeg -y -i "{file_path}" -ss {start_time} -to {end_time} -c:v libx264 -preset fast -c:a aac -avoid_negative_ts make_zero "{output_file}"'
                command = gpu_command
            elif mode == 1:  # CPU模式
                output_file = os.path.join(output_dir, f"{base_name}_split_cpu{extension}")
                # CPU编码：直接使用libx264
                command = f'ffmpeg -y -i "{file_path}" -ss {start_time} -to {end_time} -c:v libx264 -preset fast -c:a aac -avoid_negative_ts make_zero "{output_file}"'
                fallback_command = None
            else:  # 快速复制模式
                output_file = os.path.join(output_dir, f"{base_name}_split_fast{extension}")
                # 快速复制：直接复制数据流
                command = f'ffmpeg -y -ss {start_time} -i "{file_path}" -to {end_time} -c copy -avoid_negative_ts make_zero "{output_file}"'
                fallback_command = None

            # 执行命令
            result = None
            mode_names = ["GPU加速", "CPU", "快速复制"]

            if mode == 0:  # GPU加速模式
                result = None

                if command:  # 有NVIDIA GPU命令
                    print("尝试NVIDIA GPU硬件加速...")
                    self.progress_queue.put({
                        'type': 'status',
                        'message': '尝试NVIDIA GPU加速...'
                    })

                    returncode = self.run_ffmpeg_with_progress(command, self.total_frames, "NVIDIA GPU")
                    if returncode == 0:
                        print("NVIDIA GPU编码成功！")
                        result = type('obj', (object,), {'returncode': 0})()
                    else:
                        print(f"NVIDIA GPU编码失败: {self.last_error}")

                # 如果没有GPU支持或GPU失败，回退到CPU
                if not result or result.returncode != 0:
                    if fallback_command:
                        print("回退到CPU编码...")
                        self.progress_queue.put({
                            'type': 'status',
                            'message': '回退到CPU编码...'
                        })
                        returncode = self.run_ffmpeg_with_progress(fallback_command, self.total_frames, "CPU")
                        result = type('obj', (object,), {'returncode': returncode, 'stderr': self.last_error})()
                    else:
                        result = type('obj', (object,), {'returncode': 1, 'stderr': self.last_error})()
            else:
                # CPU模式或快速复制模式
                print(f"使用{mode_names[mode]}模式: {command}")
                if mode == 2:  # 快速复制模式，使用传统方式
                    result = subprocess.run(command, shell=True, capture_output=True,
                                          encoding='utf-8', errors='ignore')
                else:  # CPU模式，使用进度显示
                    self.progress_queue.put({
                        'type': 'status',
                        'message': f'使用{mode_names[mode]}模式处理...'
                    })
                    returncode = self.run_ffmpeg_with_progress(command, self.total_frames, mode_names[mode])
                    result = type('obj', (object,), {'returncode': returncode, 'stderr': self.last_error})()

            # 停止进度检查
            self._progress_check_id = False

            if result and result.returncode == 0:
                self.progress_queue.put({
                    'type': 'status',
                    'message': '视频分割完成！'
                })
                time.sleep(0.5)  # 让用户看到完成消息
                self.stop_progress()  # 停止进度条
                self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
                messagebox.showinfo("视频分割", "视频分割完成！")
            else:
                self.stop_progress()  # 停止进度条
                self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
                error_msg = "分割过程中发生错误。"
                if hasattr(result, 'stderr') and result.stderr:
                    if isinstance(result.stderr, bytes):
                        error_msg += f"\n{result.stderr.decode('utf-8', errors='ignore')}"
                    else:
                        error_msg += f"\n{result.stderr}"
                elif self.last_error:
                    error_msg += f"\n{self.last_error}"
                messagebox.showerror("错误", error_msg)
        else:
            self._progress_check_id = False
            self.stop_progress()  # 停止进度条
            self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
            messagebox.showerror("错误", "请正确填写所有字段。")

        # 重置处理状态
        self.is_processing = False
        self.current_process = None

    def on_closing(self):
        '''处理窗口关闭事件'''
        if self.is_processing and self.current_process:
            # 如果正在处理，询问用户是否确认关闭
            import tkinter.messagebox as msgbox
            result = msgbox.askyesno("确认关闭",
                                   "视频分割正在进行中，关闭程序将终止当前任务。\n确定要关闭吗？")
            if result:
                self.terminate_current_process()
                self.destroy_window()
            # 如果用户选择"否"，则不关闭窗口
        else:
            # 没有正在进行的任务，直接关闭
            self.destroy_window()

    def terminate_current_process(self):
        '''终止当前的ffmpeg进程'''
        if self.current_process:
            try:
                # 终止进程
                self.current_process.terminate()
                # 等待进程结束，最多等待3秒
                try:
                    self.current_process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    # 如果3秒后还没结束，强制杀死进程
                    self.current_process.kill()
                    self.current_process.wait()
                print("已终止ffmpeg进程")
            except Exception as e:
                print(f"终止进程时出错: {e}")
            finally:
                self.current_process = None
                self.is_processing = False

    def destroy_window(self):
        '''销毁窗口'''
        try:
            # 获取顶级窗口
            top_window = self.main_frame.winfo_toplevel()
            top_window.destroy()
        except:
            pass


if __name__ == '__main__':
    vp_start_gui()